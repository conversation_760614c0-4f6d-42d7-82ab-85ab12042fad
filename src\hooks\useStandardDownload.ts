import { useCallback } from 'react';
import type { DownloadItem, UseStandardDownloadProps } from '../types';
import { DownloadStatus } from '../types';
import {
  downloadFileWithHeaders,
  formatSpeed,
  createDownloadController,
  detectLiveStream
} from '../utils/downloadUtils';
import { getVideoDurationFromBlob } from '../utils/videoUtils';

/**
 * 标准文件下载Hook
 * 负责标准媒体文件的下载逻辑
 */
export function useStandardDownload({
  state,
  updateProgress,
  setDownloadController,
  setDownloadBlobUrl,
  updateDownloadDataDuration,
  saveToLocal
}: UseStandardDownloadProps) {

  // 使用下载控制器的标准下载
  const downloadWithController = useCallback(async (downloadData: DownloadItem) => {
    if (!state.pageTaskId) {
      throw new Error('页面任务ID不存在，无法开始下载');
    }

    updateProgress(downloadData.requestId, {
      status: DownloadStatus.DOWNLOADING,
      statusText: '准备下载...',
      percentage: 0
    });

    // 第一步：通知后台设置请求头
    const result = await downloadFileWithHeaders(downloadData, state.pageTaskId);
    console.log(`请求头设置完成: ${result.data?.filename || downloadData.filename}`);

    // 第二步：检测是否为直播流
    updateProgress(downloadData.requestId, {
      status: DownloadStatus.DOWNLOADING,
      statusText: '检测文件类型...',
      percentage: 5
    });

    const liveStreamInfo = await detectLiveStream(downloadData);
    console.log('直播流检测结果:', liveStreamInfo);

    // 如果是直播流，更新下载项标识
    if (liveStreamInfo.isLiveStream) {
      downloadData.isLiveStream = true;
      console.log(`检测到直播流: ${liveStreamInfo.reason}`);

      updateProgress(downloadData.requestId, {
        status: DownloadStatus.LIVE_RECORDING,
        statusText: '检测到直播流，开始录制...',
        percentage: 10
      });
    }

    // 第三步：创建下载控制器
    const controller = createDownloadController({
      requestId: downloadData.requestId,
      url: downloadData.url,
      filename: downloadData.filename || 'download'
    });

    // 设置进度回调
    controller.onProgress = (progress) => {
      const isLiveStream = downloadData.isLiveStream;
      const status = isLiveStream ? DownloadStatus.LIVE_RECORDING : DownloadStatus.DOWNLOADING;
      const statusText = isLiveStream
        ? `直播录制中... ${progress.speed ? formatSpeed(progress.speed) : ''}`
        : `下载中... ${progress.speed ? formatSpeed(progress.speed) : ''}`;

      updateProgress(downloadData.requestId, {
        status,
        statusText,
        percentage: progress.percentage,
        downloadedSize: progress.downloadedSize,
        totalSize: progress.totalSize,
        speed: progress.speed
      });
    };

    // 设置状态变化回调
    controller.onStatusChange = (status) => {
      if (status === 'downloading') {
        const isLiveStream = downloadData.isLiveStream;
        const downloadStatus = isLiveStream ? DownloadStatus.LIVE_RECORDING : DownloadStatus.DOWNLOADING;
        const statusText = isLiveStream ? '直播录制中...' : '下载中...';

        updateProgress(downloadData.requestId, {
          status: downloadStatus,
          statusText,
          percentage: controller.progress.percentage
        });
      }
    };

    // 保存控制器到状态
    setDownloadController(controller);

    // 开始下载
    const downloadResult = await controller.start();

    if (downloadResult.success && downloadResult.blobUrl) {
      // 下载完成，保存 blob URL
      setDownloadBlobUrl(downloadResult.blobUrl);

      // 尝试获取文件时长
      const duration = await getVideoDurationFromBlob(downloadResult.blobUrl);
      if (duration) {
        updateDownloadDataDuration(duration);
        console.log('文件时长获取成功:', duration);
      }

      updateProgress(downloadData.requestId, {
        status: DownloadStatus.COMPLETED,
        statusText: '下载完成，正在保存到本地',
        percentage: 100
      });

      // 自动触发保存
      try {
        await saveToLocal(downloadResult.blobUrl);
        console.log('标准文件已自动保存到本地:', downloadData.filename);
      } catch (error) {
        console.error('自动保存失败:', error);
        updateProgress(downloadData.requestId, {
          status: DownloadStatus.ERROR,
          statusText: '自动保存失败: ' + (error as Error).message,
          percentage: 100
        });
      }
    } else {
      throw new Error(downloadResult.error || '下载失败');
    }
  }, [state.pageTaskId, updateProgress, setDownloadController, setDownloadBlobUrl, updateDownloadDataDuration, saveToLocal]);

  // 标准文件下载主函数
  const downloadStandardFile = useCallback(async (downloadData: DownloadItem) => {
    try {
      // 统一使用下载控制器模式
      await downloadWithController(downloadData);
    } catch (error) {
      const errorMessage = (error as Error).message;

      // 如果是用户主动取消（AbortError），不显示为错误
      if (errorMessage.includes('aborted') || errorMessage.includes('AbortError')) {
        console.log('下载被用户取消');
        return; // 不抛出错误，让上层正常处理
      }

      // 真正的错误情况
      console.error('标准文件下载失败:', error);
      updateProgress(downloadData.requestId, {
        status: DownloadStatus.ERROR,
        statusText: errorMessage,
        percentage: 0
      });
      throw error;
    }
  }, [downloadWithController, updateProgress]);

  return {
    downloadStandardFile,
    downloadWithController
  };
}
