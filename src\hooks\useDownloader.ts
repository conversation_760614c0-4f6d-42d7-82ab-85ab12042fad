
import { useCallback } from 'react';
import type { DownloadItem } from '../types';
import { DownloadStatus } from '../types';
import { useDownloaderState } from './useDownloaderState';
import { useDownloaderInit } from './useDownloaderInit';
import { useM3u8Download } from './useM3u8Download';
import { useStandardDownload } from './useStandardDownload';
import { useDownloadControl } from './useDownloadControl';

/**
 * 主下载器Hook
 * 整合所有下载相关的功能模块
 */
export function useDownloader() {
  // 状态管理
  const {
    state,                    // 应用全局状态，包含下载数据、进度、控制器等信息
    updateProgress,           // 更新指定请求的下载进度（百分比、状态、速度等）
    setDownloadData,          // 设置当前下载项的数据（URL、文件名、请求头等）
    setError,                 // 设置错误信息，用于显示下载失败等错误状态
    setPageTaskId,            // 设置页面任务ID，用于与后台脚本通信
    setContentScriptReady,    // 设置内容脚本就绪状态，确保扩展通信正常
    setDownloadController,    // 设置下载控制器实例，用于控制下载的暂停/继续
    setDownloadBlobUrl,       // 设置下载完成后的Blob URL，用于后续保存到本地
    markFileAsSaved,          // 标记文件为已保存状态，避免重复保存
    updateDownloadDataDuration, // 更新下载数据的时长信息
    updateDownloadDataFilename, // 更新下载数据的文件名
    setLiveRecordingController, // 设置直播录制控制器
    stopLiveRecording           // 停止直播录制
  } = useDownloaderState();

  // 下载控制模块
  const downloadControl = useDownloadControl({
    state,
    updateProgress,
    markFileAsSaved
  });

  // M3U8下载模块
  const m3u8Download = useM3u8Download({
    state,
    updateProgress,
    setDownloadBlobUrl,
    updateDownloadDataDuration,
    saveToLocal: downloadControl.saveToLocal,
    setLiveRecordingController
  });

  // 标准下载模块
  const standardDownload = useStandardDownload({
    state,
    updateProgress,
    setDownloadController,
    setDownloadBlobUrl,
    updateDownloadDataDuration,
    saveToLocal: downloadControl.saveToLocal
  });

  // 主要下载逻辑函数
  const startDownloadWithData = useCallback(async (downloadData: DownloadItem) => {
    console.log('使用指定数据开始下载:', downloadData);

    if (!state.pageTaskId) {
      console.error('页面任务ID不存在，无法开始下载');
      return;
    }

    try {
      // 检测是否为M3U8格式
      const isM3u8 = downloadData.isM3u8 || downloadData.downloadType === 'm3u8';

      if (isM3u8) {
        // M3U8流媒体下载
        await m3u8Download.downloadM3u8File(downloadData);
      } else {
        // 标准媒体文件下载
        await standardDownload.downloadStandardFile(downloadData);
      }
    } catch (error) {
      const errorMessage = (error as Error).message;

      // 如果是用户主动取消，不显示错误状态
      if (errorMessage.includes('aborted') || errorMessage.includes('AbortError')) {
        console.log('下载被用户取消');
        return; // 不设置错误状态
      }

      // 真正的错误情况
      console.error('下载失败:', error);
      updateProgress(downloadData.requestId, {
        status: DownloadStatus.ERROR,
        statusText: errorMessage,
        percentage: 0
      });
    }
  }, [state.pageTaskId, updateProgress, m3u8Download, standardDownload]);

  // 初始化模块
  useDownloaderInit({
    state,
    setPageTaskId,
    setContentScriptReady,
    setDownloadData,
    setError,
    startDownloadWithData
  });



  // 返回所有需要的函数和状态
  return {
    ...state,
    startDownloadWithData,        // 主要下载函数，接受下载数据参数
    saveToLocal: downloadControl.saveToLocal,
    closeTab: downloadControl.closeTab,
    updateProgress,
    getCurrentDownloadStatus: downloadControl.getCurrentDownloadStatus,
    retryDownload: () => downloadControl.retryDownload(startDownloadWithData),
    canSave: downloadControl.canSave,
    updateDownloadDataFilename,   // 更新下载数据的文件名
    stopLiveRecording             // 停止直播录制
  };
}
