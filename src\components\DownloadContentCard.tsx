import React, { useState, useEffect } from 'react';
import { IconEdit, IconCheck } from '@tabler/icons-react';
import type { DownloadItem } from '../types';
import { DownloadStatus } from '../types';
import { separateFilenameAndExtension, combineFilenameAndExtension } from '../utils/videoUtils';
import DownloadButton from './DownloadButton';

interface DownloadContentCardProps {
  downloadItem: DownloadItem;
  progress?: {
    percentage: number;
    downloadedSize: number;
    totalSize?: number;
    speed?: number;
    status: DownloadStatus;
    statusText: string;
  };
  onTitleChange?: (newTitle: string) => void;
  onRetry?: () => void;
  onSave?: () => void;
  onStopRecording?: () => void; // 停止录制回调
  savedFiles?: Set<string>;
}

const DownloadContentCard: React.FC<DownloadContentCardProps> = ({
  downloadItem,
  progress,
  onTitleChange,
  onRetry,
  onSave,
  onStopRecording,
  savedFiles,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editTitle, setEditTitle] = useState('');
  // 添加内部状态来跟踪已保存的标题（不含后缀）
  const [savedTitle, setSavedTitle] = useState<string | null>(null);
  // 保存原始文件的后缀名
  const [fileExtension, setFileExtension] = useState<string>('');

  // 使用 useEffect 同步外部 downloadItem.filename 的变化
  useEffect(() => {
    if (downloadItem.filename) {
      const { name, extension } = separateFilenameAndExtension(downloadItem.filename);
      setFileExtension(extension);

      // 如果没有保存过的标题，则使用分离出的文件名（不含后缀）
      if (!savedTitle) {
        setEditTitle(name || '未知文件');
      }
    } else {
      setEditTitle('未知文件');
      setFileExtension('');
    }
  }, [downloadItem.filename, savedTitle]);

  // 处理编辑开始
  const handleEditStart = () => {
    setIsEditing(true);
    // 优先使用已保存的标题，其次是分离出的文件名（不含后缀）
    if (savedTitle) {
      setEditTitle(savedTitle);
    } else if (downloadItem.filename) {
      const { name } = separateFilenameAndExtension(downloadItem.filename);
      setEditTitle(name || '未知文件');
    } else {
      setEditTitle('未知文件');
    }
  };

  // 处理编辑保存
  const handleEditSave = () => {
    if (editTitle.trim()) {
      const trimmedTitle = editTitle.trim();
      // 保存到内部状态（不含后缀）
      setSavedTitle(trimmedTitle);

      // 组合完整文件名（包含后缀）传递给父组件
      if (onTitleChange) {
        const fullFilename = combineFilenameAndExtension(trimmedTitle, fileExtension);
        onTitleChange(fullFilename);
      }
    }
    setIsEditing(false);
  };


  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  // 格式化下载速度
  const formatSpeed = (speed: number): string => {
    return formatFileSize(speed) + '/s';
  };

  // 获取进度百分比
  const getProgressPercentage = (): number => {
    return progress?.percentage || 0;
  };

  // 获取文件名或标题 - 优先显示已保存的标题（不含后缀）
  const getDisplayTitle = (): string => {
    if (savedTitle) {
      return savedTitle;
    }

    if (downloadItem.filename) {
      const { name } = separateFilenameAndExtension(downloadItem.filename);
      return name || '未知文件';
    }

    return '未知文件';
  };

  // 获取文件扩展名显示文本
  const getFileExtensionDisplay = (): string => {
    if (fileExtension) {
      // 移除点号并转换为大写
      return fileExtension.replace('.', '').toUpperCase();
    }
    return '';
  };

  // 判断是否正在下载
  const isDownloading = (): boolean => {
    return progress?.status === DownloadStatus.DOWNLOADING;
  };

  // 判断是否正在录制直播
  const isLiveRecording = (): boolean => {
    return progress?.status === DownloadStatus.LIVE_RECORDING;
  };

  // 判断是否应该显示下载信息（下载中或直播录制中）
  const shouldShowDownloadInfo = (): boolean => {
    return isDownloading() || isLiveRecording();
  };

  // 判断是否出现错误
  const isError = (): boolean => {
    return progress?.status === DownloadStatus.ERROR;
  };

  // 判断是否下载完成
  const isDownloadCompleted = (): boolean => {
    return progress?.status === DownloadStatus.COMPLETED || progress?.status === DownloadStatus.SAVED;
  };

  // 获取当前下载状态 - 无状态时返回 null
  const getCurrentDownloadStatus = (): DownloadStatus | null => {
    if (!downloadItem) return null;

    if (!progress) {
      // 检查是否已保存
      if (savedFiles?.has(downloadItem.requestId)) {
        return DownloadStatus.SAVED;
      }
      return null;
    }

    return progress.status;
  };

  // 渲染下载按钮
  const renderDownloadButton = () => {
    const currentStatus = getCurrentDownloadStatus();

    // 无状态时不显示按钮
    if (!currentStatus) {
      return null;
    }

    return (
      <DownloadButton
        status={currentStatus}
        onRetry={onRetry}
        onSave={currentStatus === DownloadStatus.SAVED ? onSave : onSave}
        onStopRecording={onStopRecording}
      />
    );
  };

  return (
    <div className="flex flex-col justify-center items-center gap-4 w-full max-w-4xl h-80">

      {/* 标题和编辑按钮 */}
      <div className="flex items-center justify-center gap-2 w-full max-w-lg">
        {/* 文件扩展名徽章 */}
        {getFileExtensionDisplay() && (
          <div className="flex flex-row justify-center items-center px-2.5 py-0.5 gap-1 w-14 h-6 bg-gray-100 rounded-md flex-none">
            <span className="text-xs font-medium text-gray-700">
              {getFileExtensionDisplay()}
            </span>
          </div>
        )}

        {isEditing ? (
          <>
            <input
              type="text"
              value={editTitle}
              onChange={(e) => setEditTitle(e.target.value)}
              className="text-base font-medium text-gray-900 text-center w-[768px] bg-white border border-gray-300 rounded px-2 py-1 focus:outline-none focus:border-blue-500"
              autoFocus
            />
            <button
              onClick={handleEditSave}
              className="flex-shrink-0 p-1 text-black hover:text-gray-700 rounded-lg"
            >
              <IconCheck className="w-6 h-6" />
            </button>
          </>
        ) : (
          <>
            <h3 className="text-base font-medium text-gray-900 text-center max-w-md truncate">
              {getDisplayTitle()}
            </h3>
            <button
              onClick={handleEditStart}
              className="flex-shrink-0 p-1 text-gray-900 hover:text-gray-600 rounded-lg"
            >
              <IconEdit className="w-6 h-6" />
            </button>
          </>
        )}
      </div>

      {/* 进度条 - 在下载或直播录制状态时显示 */}
      {shouldShowDownloadInfo() && (
        <div className="w-full max-w-3xl">
          <div className="w-full bg-gray-200 rounded-sm h-1.5 relative overflow-hidden">
            {isLiveRecording() ? (
              // 直播录制时显示满的动态效果
              <div className="w-full h-1.5 bg-blue-600 rounded-sm relative overflow-hidden">
                {/* 脉冲背景效果 */}
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500 via-blue-400 to-blue-500 animate-pulse"></div>
                {/* 流光效果 - 使用 Tailwind 的 animate-bounce 作为替代，或者使用内联样式 */}
                <div
                  className="absolute top-0 left-0 h-full w-16 bg-gradient-to-r from-transparent via-white to-transparent opacity-40"
                  style={{
                    animation: 'shimmer 2s ease-in-out infinite'
                  }}
                  ref={(el) => {
                    if (el && !document.head.querySelector('style[data-shimmer]')) {
                      const style = document.createElement('style');
                      style.setAttribute('data-shimmer', 'true');
                      style.textContent = `
                        @keyframes shimmer {
                          0% { transform: translateX(-100%); }
                          100% { transform: translateX(400%); }
                        }
                      `;
                      document.head.appendChild(style);
                    }
                  }}
                ></div>
              </div>
            ) : (
              // 普通下载时显示百分比进度条
              <div
                className="bg-blue-600 h-1.5 rounded-sm transition-all duration-300"
                style={{ width: `${getProgressPercentage()}%` }}
              />
            )}
          </div>
        </div>
      )}

      {/* 下载进度信息 - 只在下载中、录制中或错误时显示 */}
      {progress && (shouldShowDownloadInfo() || isError()) && (
        <div className="flex items-center gap-6 text-xs">
          {isError() ? (
            // 错误状态时显示错误消息
            <span className="text-red-600">
              {progress.statusText || '下载出现错误'}
            </span>
          ) : (
            // 下载中或录制中时显示进度信息
            typeof progress.downloadedSize === 'number' && progress.downloadedSize >= 0 && (
              <>
                <span className="text-gray-900">
                  {/* 已下载大小 */}
                  {typeof progress.downloadedSize === 'number' && progress.downloadedSize >= 0
                    ? formatFileSize(progress.downloadedSize)
                    : ''}
                  {/* 总大小 - 只有当总大小是有效数字时才显示 / 和总大小 */}
                  {typeof progress.totalSize === 'number' && progress.totalSize > 0 && (
                    <> / {formatFileSize(progress.totalSize)}</>
                  )}
                </span>
                {/* 下载速度 - 只在正在下载或录制时显示 */}
                {typeof progress.speed === 'number' && progress.speed > 0 && (
                  <span className="text-gray-900">{formatSpeed(progress.speed)}</span>
                )}
              </>
            )
          )}
        </div>
      )}

      {/* 下载完成信息 - 显示文件大小和视频时长 */}
      {isDownloadCompleted() && (
        <div className="flex items-center gap-6 text-xs text-gray-900">
          {/* 文件大小 - 优先显示totalSize，如果没有则显示downloadedSize */}
          {(typeof progress?.totalSize === 'number' && progress.totalSize > 0) ? (
            <span>{formatFileSize(progress.totalSize)}</span>
          ) : (typeof progress?.downloadedSize === 'number' && progress.downloadedSize > 0) && (
            <span>{formatFileSize(progress.downloadedSize)}</span>
          )}
          {/* 视频时长 - 只有当时长存在且不为空时才显示 */}
          {downloadItem.duration && downloadItem.duration.trim() && (
            <span>{downloadItem.duration}</span>
          )}
        </div>
      )}

      {/* 下载按钮 */}
      {renderDownloadButton()}

    </div>

  );
};

export default DownloadContentCard;
